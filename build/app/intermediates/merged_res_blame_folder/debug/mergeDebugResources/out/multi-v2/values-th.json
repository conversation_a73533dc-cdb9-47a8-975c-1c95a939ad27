{"logs": [{"outputFile": "com.nudron.water_metering.app-mergeDebugResources-47:/values-th/values-th.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/ff9de53921e81b72effb184a4ee977a5/transformed/browser-1.8.0/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "56,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5748,6036,6135,6246", "endColumns": "102,98,110,97", "endOffsets": "5846,6130,6241,6339"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/71d91ec26cc715ac194e9af65b3a48ac/transformed/appcompat-1.6.1/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,7767", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,7844"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/res/values-th/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3449,3555,3702,3825,3932,4068,4192,4311,4548,4692,4797,4944,5066,5206,5357,5421,5489", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "3550,3697,3820,3927,4063,4187,4306,4414,4687,4792,4939,5061,5201,5352,5416,5484,5568"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,259,372,499,625,751,871,995,1090,1219,1348", "endColumns": "104,98,112,126,125,125,119,123,94,128,128,114", "endOffsets": "155,254,367,494,620,746,866,990,1085,1214,1343,1458"}, "to": {"startLines": "54,57,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5573,5851,6344,6457,6584,6710,6836,6956,7080,7175,7304,7433", "endColumns": "104,98,112,126,125,125,119,123,94,128,128,114", "endOffsets": "5673,5945,6452,6579,6705,6831,6951,7075,7170,7299,7428,7543"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c0dfc2e5d5f124c9ed5c4a17b0deea03/transformed/preference-1.2.1/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,480,648,728", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "170,256,336,475,643,723,801"}, "to": {"startLines": "55,58,72,73,76,77,78", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5678,5950,7548,7628,7950,8118,8198", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "5743,6031,7623,7762,8113,8193,8271"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/res/values-th/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4419", "endColumns": "128", "endOffsets": "4543"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "29,30,31,32,33,34,35,75", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2734,2830,2933,3031,3129,3232,3337,7849", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "2825,2928,3026,3124,3227,3332,3444,7945"}}]}]}