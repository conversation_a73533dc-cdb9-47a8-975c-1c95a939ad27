{"logs": [{"outputFile": "com.nudron.water_metering.app-mergeDebugResources-47:/values-pl/values-pl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,249,374,512,665,792,920,1067,1167,1301,1440", "endColumns": "103,89,124,137,152,126,127,146,99,133,138,123", "endOffsets": "154,244,369,507,660,787,915,1062,1162,1296,1435,1559"}, "to": {"startLines": "54,57,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5806,6080,6579,6704,6842,6995,7122,7250,7397,7497,7631,7770", "endColumns": "103,89,124,137,152,126,127,146,99,133,138,123", "endOffsets": "5905,6165,6699,6837,6990,7117,7245,7392,7492,7626,7765,7889"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/res/values-pl/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4544", "endColumns": "139", "endOffsets": "4679"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/res/values-pl/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3554,3658,3826,3948,4058,4209,4334,4445,4684,4855,4964,5139,5267,5426,5587,5656,5722", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "3653,3821,3943,4053,4204,4329,4440,4539,4850,4959,5134,5262,5421,5582,5651,5717,5801"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/ff9de53921e81b72effb184a4ee977a5/transformed/browser-1.8.0/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "56,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5980,6261,6360,6475", "endColumns": "99,98,114,103", "endOffsets": "6075,6355,6470,6574"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "29,30,31,32,33,34,35,75", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2817,2914,3016,3114,3213,3327,3432,8191", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "2909,3011,3109,3208,3322,3427,3549,8287"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c0dfc2e5d5f124c9ed5c4a17b0deea03/transformed/preference-1.2.1/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,266,346,480,649,730", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "170,261,341,475,644,725,802"}, "to": {"startLines": "55,58,72,73,76,77,78", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5910,6170,7894,7974,8292,8461,8542", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "5975,6256,7969,8103,8456,8537,8614"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/71d91ec26cc715ac194e9af65b3a48ac/transformed/appcompat-1.6.1/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,8108", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,8186"}}]}]}